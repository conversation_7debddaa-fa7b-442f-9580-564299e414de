<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 600" width="1000" height="600">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0B2545"/>
      <stop offset="100%" stop-color="#13315C"/>
    </linearGradient>
    
    <!-- Digital Circuit Pattern -->
    <pattern id="circuit-pattern" patternUnits="userSpaceOnUse" width="100" height="100">
      <path d="M10,10 L90,10 L90,90 L10,90 Z" fill="none" stroke="#0389D0" stroke-width="0.5" opacity="0.3"/>
      <path d="M10,10 L10,40 M30,10 L30,70 M50,10 L50,90 M70,10 L70,50 M90,10 L90,30" stroke="#0389D0" stroke-width="0.5" opacity="0.3"/>
      <path d="M10,50 L40,50 M10,70 L20,70 M30,30 L60,30 M70,70 L90,70 M50,50 L90,50" stroke="#0389D0" stroke-width="0.5" opacity="0.3"/>
      <circle cx="10" cy="10" r="3" fill="#0389D0" opacity="0.5"/>
      <circle cx="50" cy="50" r="3" fill="#0389D0" opacity="0.5"/>
      <circle cx="70" cy="70" r="3" fill="#0389D0" opacity="0.5"/>
      <circle cx="30" cy="30" r="3" fill="#0389D0" opacity="0.5"/>
      <circle cx="90" cy="50" r="3" fill="#0389D0" opacity="0.5"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="600" fill="url(#bg-gradient)"/>
  <rect width="1000" height="600" fill="url(#circuit-pattern)"/>
  
  <!-- Main Digital Element -->
  <g transform="translate(500, 300)">
    <!-- Digital Wave -->
    <path d="M-400,0 L-350,0 L-350,-80 L-300,-80 L-300,0 L-250,0 L-250,80 L-200,80 L-200,0 L-150,0 L-150,-80 L-100,-80 L-100,0 L-50,0 L-50,80 L0,80 L0,0 L50,0 L50,-80 L100,-80 L100,0 L150,0 L150,80 L200,80 L200,0 L250,0 L250,-80 L300,-80 L300,0 L350,0 L350,80 L400,80" 
          fill="none" stroke="#0389D0" stroke-width="3" opacity="0.8">
      <animate attributeName="stroke-dashoffset" from="1000" to="0" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="stroke-dasharray" values="0,0; 5,5" dur="0.5s" begin="5s" fill="freeze"/>
    </path>
    
    <!-- Circular Element -->
    <circle cx="0" cy="0" r="150" fill="none" stroke="#0389D0" stroke-width="2" opacity="0.7">
      <animate attributeName="r" from="130" to="150" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" from="0.5" to="0.8" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Binary Data -->
    <g fill="#FFFFFF" font-family="monospace" font-size="12" opacity="0.4">
      <text x="-180" y="-120">1010110101</text>
      <text x="-160" y="-100">0101010011</text>
      <text x="-140" y="-80">1101010110</text>
      <text x="-120" y="-60">0101011010</text>
      <text x="-100" y="-40">1010101011</text>
      <text x="100" y="40">0101010110</text>
      <text x="120" y="60">1010110101</text>
      <text x="140" y="80">0110101001</text>
      <text x="160" y="100">1010101101</text>
      <text x="180" y="120">0101010110</text>
    </g>
    
    <!-- Floating Data Packets -->
    <g>
      <rect x="-30" y="-30" width="60" height="60" fill="#0389D0" opacity="0.2" rx="10" ry="10">
        <animate attributeName="opacity" values="0.2;0.5;0.2" dur="3s" repeatCount="indefinite"/>
      </rect>
      <rect x="-25" y="-25" width="50" height="50" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.5" rx="8" ry="8"/>
      
      <rect x="-130" y="70" width="40" height="40" fill="#0389D0" opacity="0.2" rx="8" ry="8">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="4s" repeatCount="indefinite"/>
      </rect>
      <rect x="-125" y="75" width="30" height="30" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.5" rx="6" ry="6"/>
      
      <rect x="90" y="-90" width="40" height="40" fill="#0389D0" opacity="0.2" rx="8" ry="8">
        <animate attributeName="opacity" values="0.2;0.4;0.2" dur="5s" repeatCount="indefinite"/>
      </rect>
      <rect x="95" y="-85" width="30" height="30" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.5" rx="6" ry="6"/>
    </g>
  </g>
  
  <!-- Floating Data Elements -->
  <g fill="#FFFFFF" opacity="0.6">
    <circle cx="200" cy="150" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="250" cy="180" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="700" cy="220" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="750" cy="320" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="2.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="650" cy="420" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="320" cy="450" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="4.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="420" cy="120" r="1">
      <animate attributeName="opacity" values="0.6;0;0.6" dur="3.7s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg> 