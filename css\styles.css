/* 
   Infinitel Maris Website Styles
   Modern telecommunications design
   Primary colors: #165083, #0389D0
*/

/* Base Styles & Reset */
:root {
    --primary-dark: #165083;
    --primary-light: #0389D0;
    --secondary: #f8f9fa;
    --text-dark: #212529;
    --text-light: #f8f9fa;
    --text-muted: #6c757d;
    --border-color: #e9ecef;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --white: #ffffff;
    --black: #000000;
    --transition: all 0.3s ease;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: var(--primary-dark);
    transition: var(--transition);
}

a:hover {
    color: var(--primary-light);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    color: var(--white);
}

h2 {
    font-size: 2rem;
    color: var(--primary-dark);
}

h3 {
    font-size: 1.75rem;
    color: var(--primary-dark);
}

h4 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

.text-center {
    text-align: center;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    text-align: center;
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-dark);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-light);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-dark);
    color: var(--primary-dark);
}

.btn-outline:hover {
    background-color: var(--primary-dark);
    color: var(--white);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-dark);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-light);
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

.header.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-dark);
    position: relative;
}

.logo-text::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--primary-dark), var(--primary-light));
    border-radius: 2px;
}

.main-nav .nav-links {
    display: flex;
}

.main-nav .nav-links li {
    margin-left: 2rem;
}

.main-nav .nav-links a {
    color: var(--text-dark);
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
}

.main-nav .nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, var(--primary-dark), var(--primary-light));
    transition: var(--transition);
}

.main-nav .nav-links a:hover::after,
.main-nav .nav-links a.active::after {
    width: 100%;
}

.mobile-nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--primary-dark);
    margin: 2px 0;
    border-radius: 2px;
    transition: var(--transition);
}

/* Hero/Home Section */
.home-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding-top: 80px;
}

.carousel-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.carousel {
    width: 100%;
    height: 100%;
    position: relative;
}

.carousel-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1.5s ease-in-out;
    transform: scale(1.05);
}

.carousel-item.active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 1.5s ease-in-out, transform 8s ease-out;
}

.carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.carousel-item .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.6) 50%,
        rgba(0, 0, 0, 0.8) 100%
    );
}

.carousel-dots {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    z-index: 10;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    margin: 0 8px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.dot:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--white);
    transform: scale(0);
    transition: transform 0.3s ease;
    border-radius: 50%;
}

.dot.active:after {
    transform: scale(1);
}

.dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

.dot.active {
    transform: scale(1.2);
    background-color: var(--primary-light);
}

.home-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 5rem 0;
    color: var(--white);
}

.graphics-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    overflow: hidden;
}

.network-graphic {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.15;
    background: 
        radial-gradient(circle at 10% 20%, transparent 6px, var(--primary-light) 7px, transparent 7px) 0 0 / 60px 60px,
        radial-gradient(circle at 90% 80%, transparent 6px, var(--primary-light) 7px, transparent 7px) 0 0 / 60px 60px;
    animation: pulse 4s ease-in-out infinite alternate;
}

.network-graphic:before,
.network-graphic:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.network-graphic:before {
    background: 
        linear-gradient(to right, transparent 49.5%, var(--primary-light) 50%, transparent 50.5%) 0 0 / 80px 100%,
        linear-gradient(to bottom, transparent 49.5%, var(--primary-light) 50%, transparent 50.5%) 0 0 / 100% 80px;
    opacity: 0.07;
    animation: gridPulse 8s linear infinite alternate;
}

.network-graphic:after {
    background: 
        radial-gradient(circle at 50% 50%, transparent 85%, var(--primary-light) 95%, transparent 100%) 0 0 / 180px 180px;
    opacity: 0.1;
    animation: ripple 15s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.12;
    }
    100% {
        opacity: 0.18;
    }
}

@keyframes gridPulse {
    0% {
        opacity: 0.05;
    }
    50% {
        opacity: 0.09;
    }
    100% {
        opacity: 0.05;
    }
}

@keyframes ripple {
    0% {
        background-size: 150px 150px;
        opacity: 0.08;
    }
    50% {
        background-size: 200px 200px;
        opacity: 0.12;
    }
    100% {
        background-size: 150px 150px;
        opacity: 0.08;
    }
}

.hero-text {
    max-width: 800px;
    text-align: center;
    padding: 2.5rem;
    border-radius: var(--border-radius);
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
    opacity: 0;
    transform: translateY(20px);
    display: none;
}

.hero-text.active {
    opacity: 1;
    transform: translateY(0);
    display: block;
}

.hero-text h1 {
    margin-bottom: 1.2rem;
    position: relative;
    display: inline-block;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-text h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 30%;
    width: 40%;
    height: 3px;
    background: linear-gradient(to right, var(--primary-dark), var(--primary-light));
    border-radius: 2px;
    animation: expandWidth 1.5s ease-out;
}

@keyframes expandWidth {
    0% {
        width: 0;
        left: 50%;
    }
    100% {
        width: 40%;
        left: 30%;
    }
}

.hero-text h2 {
    color: var(--white);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    opacity: 0.9;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.hero-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.7;
    opacity: 0.8;
}

.cta-group {
    margin-top: 2rem;
    animation: fadeIn 1s 0.5s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.cta-group h3 {
    color: var(--primary-light);
    margin-bottom: 0.7rem;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.cta-group p {
    opacity: 0.9;
    margin-bottom: 1.2rem;
    font-size: 1.05rem;
}

.cta-group .btn {
    margin-top: 0.5rem;
    padding: 0.8rem 2rem;
    font-size: 1.05rem;
    box-shadow: 0 5px 15px rgba(3, 137, 208, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.cta-group .btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
    z-index: -1;
}

.cta-group .btn:hover:before {
    left: 100%;
}

/* Section Common Styles */
.section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    display: inline-block;
    position: relative;
    margin-bottom: 1rem;
}

.section-divider {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
}

.section-divider span {
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-dark), var(--primary-light));
    border-radius: 2px;
}

/* Voice Section */
.voice-section {
    background-color: var(--secondary);
}

.voice-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 2rem;
}

.voice-text {
    flex: 1;
    min-width: 300px;
}

.voice-feature {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    background-color: var(--white);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.voice-feature p {
    margin-bottom: 0;
}

.voice-image {
    flex: 1;
    min-width: 300px;
    height: 400px;
    position: relative;
}

.image-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(145deg, var(--primary-dark), var(--primary-light));
    border-radius: var(--border-radius);
    overflow: hidden;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    opacity: 0.2;
    background-color: var(--white);
}

.element-1 {
    width: 150px;
    height: 150px;
    top: 20%;
    left: 30%;
    animation: float 8s ease-in-out infinite;
}

.element-2 {
    width: 100px;
    height: 100px;
    bottom: 30%;
    right: 20%;
    animation: float 6s ease-in-out infinite 1s;
}

.element-3 {
    width: 70px;
    height: 70px;
    top: 60%;
    left: 15%;
    animation: float 7s ease-in-out infinite 0.5s;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

/* DID Numbers Section */
.did-numbers-section {
    background-color: var(--white);
}

.did-intro {
    max-width: 800px;
    margin: 0 auto 3rem;
    text-align: center;
}

.did-table-container {
    max-width: 1000px;
    margin: 0 auto;
}

.table-responsive {
    overflow-x: auto;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.did-table {
    width: 100%;
    border-collapse: collapse;
}

.did-table th,
.did-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.did-table th {
    background-color: var(--primary-dark);
    color: var(--white);
    font-weight: 600;
}

.did-table th:first-child {
    border-top-left-radius: var(--border-radius);
}

.did-table th:last-child {
    border-top-right-radius: var(--border-radius);
}

.did-table tr:nth-child(even) {
    background-color: var(--secondary);
}

.did-table tr:hover {
    background-color: rgba(3, 137, 208, 0.1);
}

.table-note {
    text-align: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* News & Events Section */
.news-events-section {
    background-color: var(--secondary);
}

.news-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.subtitle {
    color: var(--primary-light);
    font-style: italic;
    margin-bottom: 1.5rem;
}

.news-benefits {
    margin-bottom: 3rem;
}

.news-benefits h3 {
    text-align: center;
    margin-bottom: 2rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background-color: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(145deg, var(--primary-dark), var(--primary-light));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.benefit-card h4 {
    margin-bottom: 1rem;
    color: var(--primary-dark);
}

.benefit-card p {
    color: var(--text-muted);
    margin-bottom: 0;
}

.events-section {
    margin-bottom: 3rem;
    text-align: center;
}

.events-section h3 {
    margin-bottom: 1rem;
}

.events-list {
    display: flex;
    flex-direction: column;
    max-width: 600px;
    margin: 0 auto;
}

.event-item {
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.event-item:hover {
    transform: translateX(5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.event-item p {
    margin-bottom: 0;
    font-weight: 500;
}

.gallery {
    padding-top: 1rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    filter: brightness(1.1);
}

/* Footer */
.footer {
    background-color: var(--primary-dark);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo a {
    color: var(--white);
}

.footer-logo .logo-text {
    color: var(--white);
}

.footer-logo .logo-text::after {
    background: var(--white);
}

.footer-logo p {
    margin-top: 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.footer-links h4,
.footer-contact h4 {
    color: var(--white);
    margin-bottom: 1.5rem;
    position: relative;
}

.footer-links h4::after,
.footer-contact h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-light);
}

.footer-links ul li {
    margin-bottom: 0.75rem;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.footer-links ul li a:hover {
    color: var(--white);
    padding-left: 5px;
}

.footer-contact ul li {
    display: flex;
    margin-bottom: 1rem;
}

.footer-contact ul li i {
    margin-right: 1rem;
    color: var(--primary-light);
}

.footer-contact ul li p {
    margin-bottom: 0;
}

.footer-contact ul li a {
    color: rgba(255, 255, 255, 0.8);
}

.footer-contact ul li a:hover {
    color: var(--white);
}

.footer-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
    margin-bottom: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.social-links {
    display: flex;
}

.social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.75rem;
    transition: var(--transition);
}

.social-link:hover {
    background-color: var(--primary-light);
    color: var(--white);
    transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    html {
        font-size: 15px;
    }
    
    .home-content {
        padding: 3rem 0;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
    
    .main-nav {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background-color: var(--white);
        transition: var(--transition);
        z-index: 1000;
    }
    
    .main-nav.active {
        left: 0;
    }
    
    .main-nav .nav-links {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    
    .main-nav .nav-links li {
        margin: 1.5rem 0;
    }
    
    .mobile-nav-toggle {
        display: flex;
    }
    
    .hero-text {
        padding: 1.5rem;
    }
    
    .voice-content, 
    .footer-bottom {
        flex-direction: column;
    }
    
    .voice-image {
        height: 300px;
        margin-top: 2rem;
    }
    
    .copyright {
        margin-bottom: 1rem;
    }
    
    .social-links {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .section {
        padding: 3rem 0;
    }
    
    .benefit-card {
        padding: 1.5rem;
    }
    
    .footer {
        padding: 3rem 0 1.5rem;
    }
    
    .gallery-item img {
        height: 200px;
    }
} 