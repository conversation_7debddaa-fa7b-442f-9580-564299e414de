/**
 * Infinitel Maris Website Scripts
 * Features:
 * - Image Carousel
 * - Back to Top Button
 * - Sticky Header
 * - Mobile Navigation
 * - Smooth Scrolling
 * - Network Graphics Animation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all features
    initCarousel();
    initBackToTop();
    initStickyHeader();
    initMobileNav();
    initSmoothScroll();
    initNetworkGraphics();
});

// Carousel Functionality
function initCarousel() {
    const carousel = document.querySelector('.carousel');
    const items = document.querySelectorAll('.carousel-item');
    const dots = document.querySelectorAll('.dot');
    const heroTexts = document.querySelectorAll('.hero-text');
    const interval = 8000; // Slide interval in ms (increased for better UX)
    
    let currentIndex = 0;
    
    // Set up automatic carousel
    let slideInterval = setInterval(nextSlide, interval);
    
    // Add click events to dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            clearInterval(slideInterval);
            currentIndex = index;
            updateCarousel();
            slideInterval = setInterval(nextSlide, interval);
        });
    });
    
    function nextSlide() {
        currentIndex = (currentIndex + 1) % items.length;
        updateCarousel();
    }
    
    function updateCarousel() {
        // Update slides
        items.forEach(item => {
            item.classList.remove('active');
        });
        items[currentIndex].classList.add('active');
        
        // Update hero text
        heroTexts.forEach(text => {
            text.classList.remove('active');
        });
        const activeText = document.querySelector(`.hero-text[data-slide="${currentIndex}"]`);
        if (activeText) {
            activeText.classList.add('active');
        }
        
        // Update dots
        dots.forEach(dot => {
            dot.classList.remove('active');
        });
        dots[currentIndex].classList.add('active');
    }
}

// Back to Top Button
function initBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    const scrollThreshold = window.innerHeight * 0.3; // 30% of viewport height
    
    // Show/hide button based on scroll position
    window.addEventListener('scroll', () => {
        if (window.scrollY > scrollThreshold) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    // Scroll to top when clicked
    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Sticky Header on Scroll
function initStickyHeader() {
    const header = document.querySelector('.header');
    const scrollThreshold = 50;
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > scrollThreshold) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Mobile Navigation
function initMobileNav() {
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mainNav = document.querySelector('.main-nav');
    const navLinks = document.querySelectorAll('.nav-links a');
    
    // Toggle mobile menu
    mobileNavToggle.addEventListener('click', () => {
        mainNav.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });
    
    // Close mobile menu when clicking a nav link
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            mainNav.classList.remove('active');
            document.body.classList.remove('nav-open');
        });
    });
}

// Smooth Scrolling for Anchor Links
function initSmoothScroll() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Prevent default anchor behavior
            e.preventDefault();
            
            // Get the target section
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetSection = document.querySelector(targetId);
            if (!targetSection) return;
            
            // Calculate scroll position (accounting for header height)
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetSection.offsetTop - headerHeight;
            
            // Scroll to the target
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
            
            // Update active nav link
            updateActiveNavLink(targetId);
        });
    });
    
    // Update active nav link on scroll
    window.addEventListener('scroll', () => {
        const scrollPosition = window.scrollY + document.querySelector('.header').offsetHeight + 10;
        
        // Find which section is currently in view
        const sections = document.querySelectorAll('section[id]');
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                updateActiveNavLink('#' + section.getAttribute('id'));
            }
        });
    });
    
    function updateActiveNavLink(targetId) {
        // Remove active class from all links
        navLinks.forEach(link => {
            link.classList.remove('active');
        });
        
        // Add active class to matching link
        const activeLink = document.querySelector(`a[href="${targetId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }
}

// Network Graphic Animation
function initNetworkGraphics() {
    const networkGraphic = document.querySelector('.network-graphic');
    const homeSection = document.querySelector('.home-section');
    if (!networkGraphic || !homeSection) return;
    
    // Create dynamic connection elements
    createConnectionNodes(networkGraphic, 15); // Create 15 connection nodes
    
    // Mouse movement interaction
    homeSection.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX;
        const mouseY = e.clientY;
        
        // Adjust network opacity and position based on mouse movement
        const xOffset = (mouseX / window.innerWidth - 0.5) * 20;
        const yOffset = (mouseY / window.innerHeight - 0.5) * 10;
        
        // Apply subtle movement to the network graphic
        networkGraphic.style.transform = `translate(${xOffset}px, ${yOffset}px)`;
        
        // Adjust connection lines based on mouse position
        const connectionLines = document.querySelectorAll('.connection-line');
        connectionLines.forEach(line => {
            const dx = parseFloat(line.getAttribute('data-x')) - mouseX / window.innerWidth;
            const dy = parseFloat(line.getAttribute('data-y')) - mouseY / window.innerHeight;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            // Make connections more visible when mouse is closer
            line.style.opacity = Math.max(0.1, 0.4 - distance * 0.5);
            line.style.height = `${Math.max(1, 3 - distance * 2)}px`;
        });
    });
    
    // Base animation with subtle movement
    let offset = 0;
    
    function animateNetwork() {
        offset = (offset + 0.2) % 60; // Slower animation for smoother effect
        networkGraphic.style.backgroundPosition = `${offset}px ${offset}px, ${-offset}px ${-offset}px`;
        requestAnimationFrame(animateNetwork);
    }
    
    animateNetwork();
}

// Create dynamic connection nodes and lines
function createConnectionNodes(container, count) {
    const fragment = document.createDocumentFragment();
    
    // Create container for nodes and connections
    const connectionsContainer = document.createElement('div');
    connectionsContainer.className = 'connections-container';
    connectionsContainer.style.position = 'absolute';
    connectionsContainer.style.width = '100%';
    connectionsContainer.style.height = '100%';
    connectionsContainer.style.top = '0';
    connectionsContainer.style.left = '0';
    connectionsContainer.style.overflow = 'hidden';
    connectionsContainer.style.pointerEvents = 'none';
    
    // Create connection nodes
    for (let i = 0; i < count; i++) {
        // Create node
        const node = document.createElement('div');
        node.className = 'connection-node';
        
        // Random position
        const x = Math.random();
        const y = Math.random();
        
        node.style.position = 'absolute';
        node.style.left = `${x * 100}%`;
        node.style.top = `${y * 100}%`;
        node.style.width = '6px';
        node.style.height = '6px';
        node.style.borderRadius = '50%';
        node.style.backgroundColor = 'rgba(3, 137, 208, 0.5)';
        node.style.boxShadow = '0 0 8px rgba(3, 137, 208, 0.3)';
        node.style.zIndex = '1';
        node.style.transform = 'translate(-50%, -50%)';
        
        // Pulsating animation
        node.style.animation = `pulsate ${3 + Math.random() * 3}s infinite alternate ease-in-out ${Math.random() * 2}s`;
        
        // Create connection lines to a few random other nodes
        for (let j = 0; j < 2; j++) {
            // Target coordinates (another random position)
            const targetX = Math.random();
            const targetY = Math.random();
            
            // Calculate angle and distance
            const dx = targetX - x;
            const dy = targetY - y;
            const angle = Math.atan2(dy, dx) * (180 / Math.PI);
            const distance = Math.sqrt(dx * dx + dy * dy) * 100;
            
            // Create line
            const line = document.createElement('div');
            line.className = 'connection-line';
            line.setAttribute('data-x', x);
            line.setAttribute('data-y', y);
            
            line.style.position = 'absolute';
            line.style.left = `${x * 100}%`;
            line.style.top = `${y * 100}%`;
            line.style.width = `${distance}px`;
            line.style.height = '1px';
            line.style.backgroundColor = 'rgba(3, 137, 208, 0.15)';
            line.style.transformOrigin = 'left center';
            line.style.transform = `rotate(${angle}deg)`;
            line.style.zIndex = '0';
            line.style.opacity = '0.15';
            line.style.transition = 'opacity 0.5s, height 0.5s';
            
            connectionsContainer.appendChild(line);
        }
        
        connectionsContainer.appendChild(node);
    }
    
    // Add keyframe animation for pulsating effect
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulsate {
            0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
            100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.7; }
        }
    `;
    document.head.appendChild(style);
    
    fragment.appendChild(connectionsContainer);
    container.appendChild(fragment);
} 